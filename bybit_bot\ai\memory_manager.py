try:
    from safe_correlation import safe_correlation_calculation
except ImportError:
    def safe_correlation_calculation(a, b): return 0.0

"""
Persistent Memory Management System for Bybit Trading Bot
Implements self-learning capabilities and pattern recognition
OPTIMIZED FOR MAXIMUM PERFORMANCE AND MINIMUM MEMORY USAGE
"""

# CRITICAL: Fix sys.warnoptions error before any other imports
import sys
# Ensure sys.warnoptions exists
if not hasattr(sys, 'warnoptions'):
    sys.warnoptions = []

try:
    sys.warnoptions = []
except:
    pass

import json
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import hashlib
from collections import defaultdict, deque
import gc
import weakref
import warnings

# Suppress warnings for better performance
warnings.simplefilter("ignore")

from ..core.config import BotConfig
from ..core.logger import TradingBotLogger
from ..database.connection import DatabaseManager


class PatternType(Enum):
    """Types of trading patterns"""
    MARKET_CONDITION = "market_condition"
    STRATEGY_PERFORMANCE = "strategy_performance"
    RISK_EVENT = "risk_event"
    PRICE_PATTERN = "price_pattern"
    VOLUME_PATTERN = "volume_pattern"
    TIME_PATTERN = "time_pattern"
    CORRELATION_PATTERN = "correlation_pattern"


class MemoryImportance(Enum):
    """Importance levels for memories"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


@dataclass
class TradingMemory:
    """Trading memory data structure"""
    memory_id: str
    timestamp: datetime
    pattern_type: PatternType
    importance: MemoryImportance
    market_conditions: Dict[str, Any]
    strategy_used: str
    action_taken: str
    outcome: Dict[str, Any]
    context: Dict[str, Any]
    pattern_hash: str
    confidence: float
    usage_count: int
    last_accessed: datetime
    success_rate: float


@dataclass
class PatternMatch:
    """Pattern match result"""
    memory: TradingMemory
    similarity_score: float
    confidence: float
    recommended_action: str
    risk_assessment: Dict[str, Any]


class PersistentMemoryManager:
    """
    Advanced memory management system that:
    - Stores and retrieves trading experiences
    - Recognizes similar market patterns
    - Learns from trading outcomes
    - Adapts strategy parameters based on experience
    - Maintains persistent knowledge across restarts
    - OPTIMIZED FOR MAXIMUM PERFORMANCE AND MINIMUM MEMORY USAGE
    """
    
    def __init__(self, config: BotConfig, database_manager: DatabaseManager):
        self.config = config
        self.db = database_manager
        self.logger = TradingBotLogger(config)
        
        # Memory storage - OPTIMIZED FOR PERFORMANCE
        self.memories: Dict[str, TradingMemory] = {}
        self.pattern_index: Dict[str, List[str]] = defaultdict(list)
        self.recent_memories: deque = deque(maxlen=500)  # Reduced from 1000
        
        # Learning parameters - OPTIMIZED FOR EFFICIENCY
        self.similarity_threshold = 0.7
        self.min_confidence = 0.6
        self.max_memories = 5000  # Reduced from 10000
        self.memory_decay_days = 30  # Reduced from 90
        
        # MEMORY OPTIMIZATION PARAMETERS
        self.cleanup_interval = 300  # Clean up every 5 minutes
        self.max_memory_usage = 85.0  # Maximum memory usage percentage
        self.last_cleanup = datetime.utcnow()
        self.memory_pressure_threshold = 80.0
        
        # Use weak references for temporary data
        self._temp_data = weakref.WeakValueDictionary()
        
        # Initialize memory optimization
        self._initialize_memory_optimization()
        
        # Pattern recognition - OPTIMIZED WEIGHTS
        self.feature_weights = {
            'volatility': 0.25,
            'volume': 0.2,
            'trend_strength': 0.25,
            'rsi': 0.15,
            'macd': 0.15
        }
        
        # Performance tracking - OPTIMIZED STORAGE
        self.pattern_performance = defaultdict(lambda: {'success': 0, 'total': 0})
        self.strategy_memory = defaultdict(dict)

    async def initialize(self):
        try:
            self.logger.info("[INFO] Initializing Persistent Memory Manager...")
            
            # Check memory pressure before initialization
            self._check_memory_pressure()
            
            # Load existing memories from database (optimized)
            await self._load_memories_from_database()
            
            # Build pattern index (optimized)
            await self._build_pattern_index()
            
            # Load strategy memories (optimized)
            await self._load_strategy_memories()
            
            # Perform initial cleanup
            self._perform_memory_cleanup()
            
            self.logger.info(f"[OK] Memory Manager initialized with {len(self.memories)} memories")
            
        except Exception as e:
            self.logger.error(f"Error initializing memory manager: {e}")
    
    async def store_memory(self, memory_data: Dict[str, Any]) -> str:
        """Store trading memory with memory optimization"""
        try:
            # Check memory pressure before storing
            if self._check_memory_pressure():
                self.logger.info("[INFO] Memory pressure detected, performing cleanup before storing")
            
            # Create memory object
            memory = TradingMemory(
                memory_id=memory_data.get('memory_id', self._generate_memory_id()),
                timestamp=datetime.utcnow(),
                market_conditions=memory_data.get('market_conditions', {}),
                strategy_name=memory_data.get('strategy_name', ''),
                action_taken=memory_data.get('action_taken', ''),
                outcome=memory_data.get('outcome', {}),
                confidence=memory_data.get('confidence', 0.5),
                pattern_type=PatternType(memory_data.get('pattern_type', 'MARKET_CONDITION')),
                success_rate=memory_data.get('success_rate', 0.0),
                usage_count=0,
                last_accessed=datetime.utcnow()
            )
            
            # Store memory
            self.memories[memory.memory_id] = memory
            
            # Update pattern index
            pattern_hash = self._create_pattern_hash(
                memory.market_conditions,
                memory.strategy_name,
                memory.action_taken
            )
            self.pattern_index[pattern_hash].append(memory.memory_id)
            
            # Add to recent memories
            self.recent_memories.append(memory.memory_id)
            
            # Save to database (async)
            await self._save_memory_to_database(memory)
            
            # Check if cleanup is needed
            if len(self.memories) > self.max_memories * 0.9:
                self._optimize_data_structures()
            
            self.logger.info(f"[OK] Memory stored: {memory.memory_id}")
            return memory.memory_id
            
        except Exception as e:
            self.logger.error(f"Error storing memory: {e}")
            return ""
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import hashlib
from collections import defaultdict, deque
import gc
import weakref

from ..core.config import BotConfig
from ..core.logger import TradingBotLogger
from ..database.connection import DatabaseManager


class PatternType(Enum):
    """Types of trading patterns"""
    MARKET_CONDITION = "market_condition"
    STRATEGY_PERFORMANCE = "strategy_performance"
    RISK_EVENT = "risk_event"
    PRICE_PATTERN = "price_pattern"
    VOLUME_PATTERN = "volume_pattern"
    TIME_PATTERN = "time_pattern"
    CORRELATION_PATTERN = "correlation_pattern"


class MemoryImportance(Enum):
    """Importance levels for memories"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


@dataclass
class TradingMemory:
    """Individual trading memory/experience"""
    memory_id: str
    pattern_type: PatternType
    importance: MemoryImportance
    timestamp: datetime
    market_conditions: Dict[str, Any]
    strategy_used: str
    action_taken: str
    outcome: Dict[str, Any]
    context: Dict[str, Any]
    pattern_hash: str
    confidence: float
    usage_count: int
    last_accessed: datetime
    success_rate: float


@dataclass
class PatternMatch:
    """Pattern matching result"""
    memory: TradingMemory
    similarity_score: float
    confidence: float
    recommended_action: str
    risk_assessment: Dict[str, Any]


class PersistentMemoryManager:
    """
    Advanced memory management system that:
    - Stores and retrieves trading experiences
    - Recognizes similar market patterns
    - Learns from trading outcomes
    - Adapts strategy parameters based on experience
    - Maintains persistent knowledge across restarts
    - OPTIMIZED FOR MAXIMUM PERFORMANCE AND MINIMUM MEMORY USAGE
    """
    
    def __init__(self, config: BotConfig, database_manager: DatabaseManager):
        self.config = config
        self.db = database_manager
        self.logger = TradingBotLogger(config)
        
        # Memory storage - OPTIMIZED FOR PERFORMANCE
        self.memories: Dict[str, TradingMemory] = {}
        self.pattern_index: Dict[str, List[str]] = defaultdict(list)
        self.recent_memories: deque = deque(maxlen=500)  # Reduced from 1000
        
        # Learning parameters - OPTIMIZED FOR EFFICIENCY
        self.similarity_threshold = 0.7
        self.min_confidence = 0.6
        self.max_memories = 5000  # Reduced from 10000
        self.memory_decay_days = 30  # Reduced from 90
        
        # MEMORY OPTIMIZATION PARAMETERS
        self.cleanup_interval = 300  # Clean up every 5 minutes
        self.max_memory_usage = 85.0  # Maximum memory usage percentage
        self.last_cleanup = datetime.utcnow()
        self.memory_pressure_threshold = 80.0
        
        # Use weak references for temporary data
        self._temp_data = weakref.WeakValueDictionary()
        
        # Initialize memory optimization
        self._initialize_memory_optimization()
        
        # Pattern recognition - OPTIMIZED WEIGHTS
        self.feature_weights = {
            'volatility': 0.25,
            'volume': 0.2,
            'trend_strength': 0.25,
            'rsi': 0.15,
            'macd': 0.15
        }
        
        # Performance tracking - OPTIMIZED STORAGE
        self.pattern_performance = defaultdict(lambda: {'success': 0, 'total': 0})
        self.strategy_memory = defaultdict(dict)
        
    def _initialize_memory_optimization(self):
        """Initialize memory optimization systems"""
        try:
            # Enable garbage collection optimization
            gc.set_threshold(700, 10, 10)  # More aggressive GC
            
            # Clear any existing memory pressure
            self._perform_memory_cleanup()
            
            self.logger.info("[OK] Memory optimization initialized")
        except Exception as e:
            self.logger.error(f"Error initializing memory optimization: {e}")
    
    def _perform_memory_cleanup(self):
        """Perform aggressive memory cleanup"""
        try:
            # Force garbage collection
            collected = gc.collect()
            
            # Clean up old memories
            self._cleanup_old_memories()
            
            # Optimize data structures
            self._optimize_data_structures()
            
            self.logger.info(f"[OK] Memory cleanup completed - collected {collected} objects")
            
        except Exception as e:
            self.logger.error(f"Error during memory cleanup: {e}")
    
    def _cleanup_old_memories(self):
        """Clean up old memories to free space"""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=self.memory_decay_days)
            
            # Remove old memories
            old_memories = [
                mem_id for mem_id, memory in self.memories.items()
                if memory.timestamp < cutoff_date
            ]
            
            for mem_id in old_memories:
                del self.memories[mem_id]
                # Clean up pattern index
                for pattern_list in self.pattern_index.values():
                    if mem_id in pattern_list:
                        pattern_list.remove(mem_id)
            
            if old_memories:
                self.logger.info(f"[OK] Cleaned up {len(old_memories)} old memories")
                
        except Exception as e:
            self.logger.error(f"Error cleaning up old memories: {e}")
    
    def _optimize_data_structures(self):
        """Optimize data structures for memory efficiency"""
        try:
            # Limit memory collections
            if len(self.memories) > self.max_memories:
                # Remove least used memories
                sorted_memories = sorted(
                    self.memories.items(),
                    key=lambda x: (x[1].usage_count, x[1].last_accessed)
                )
                
                memories_to_remove = len(self.memories) - self.max_memories
                for i in range(memories_to_remove):
                    mem_id, _ = sorted_memories[i]
                    del self.memories[mem_id]
                    
                self.logger.info(f"[OK] Removed {memories_to_remove} least used memories")
            
            # Optimize pattern index
            for pattern_type, pattern_list in self.pattern_index.items():
                if len(pattern_list) > 1000:
                    # Keep only the most recent patterns
                    self.pattern_index[pattern_type] = pattern_list[-1000:]
                    
        except Exception as e:
            self.logger.error(f"Error optimizing data structures: {e}")
    
    def _check_memory_pressure(self):
        """Check if memory pressure cleanup is needed"""
        try:
            import psutil
            memory_usage = psutil.virtual_memory().percent
            
            if memory_usage > self.memory_pressure_threshold:
                self.logger.warning(f"[WARNING] Memory pressure detected: {memory_usage:.1f}%")
                self._perform_memory_cleanup()
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error checking memory pressure: {e}")
            return False
        
    async def initialize(self):
        """Initialize the memory manager"""
        try:
            self.logger.info("MEMORY: Initializing Persistent Memory Manager...")

            # Load existing memories from database
            await self._load_memories_from_database()

            # Build pattern index
            await self._build_pattern_index()

            # Load strategy memories
            await self._load_strategy_memories()

            self.logger.info(f"SUCCESS: Memory Manager initialized with {len(self.memories)} memories")
            
        except Exception as e:
            self.logger.error(f"ERROR: Failed to initialize Memory Manager: {e}")
            raise
    
    async def store_trading_experience(self, 
                                     market_conditions: Dict[str, Any],
                                     strategy_used: str,
                                     action_taken: str,
                                     outcome: Dict[str, Any],
                                     context: Optional[Dict[str, Any]] = None) -> str:
        """Store a new trading experience"""
        try:
            # Create pattern hash for similarity matching
            pattern_hash = self._create_pattern_hash(market_conditions, strategy_used, action_taken)
            
            # Determine importance based on outcome
            importance = self._determine_importance(outcome)
            
            # Calculate confidence based on outcome consistency
            confidence = self._calculate_confidence(pattern_hash, outcome)
            
            # Create memory
            memory_id = f"mem_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}_{hash(pattern_hash) % 10000}"
            
            memory = TradingMemory(
                memory_id=memory_id,
                pattern_type=self._classify_pattern_type(market_conditions, action_taken),
                importance=importance,
                timestamp=datetime.utcnow(),
                market_conditions=market_conditions,
                strategy_used=strategy_used,
                action_taken=action_taken,
                outcome=outcome,
                context=context or {},
                pattern_hash=pattern_hash,
                confidence=confidence,
                usage_count=0,
                last_accessed=datetime.utcnow(),
                success_rate=1.0 if outcome.get('success', False) else 0.0
            )
            
            # Store memory
            self.memories[memory_id] = memory
            self.recent_memories.append(memory_id)
            
            # Update pattern index
            self.pattern_index[pattern_hash].append(memory_id)
            
            # Save to database
            await self._save_memory_to_database(memory)
            
            # Update pattern performance
            self._update_pattern_performance(pattern_hash, outcome.get('success', False))
            
            self.logger.info(f"STORAGE: Stored trading experience: {memory_id} ({importance.value})")
            
            return memory_id
            
        except Exception as e:
            self.logger.error(f"Error storing trading experience: {e}")
            return ""
    
    async def retrieve_similar_patterns(self, 
                                      current_conditions: Dict[str, Any],
                                      strategy: str,
                                      min_similarity: Optional[float] = None) -> List[PatternMatch]:
        """Retrieve similar patterns from memory with optimization"""
        try:
            # Check memory pressure before retrieval
            if self._check_memory_pressure():
                self.logger.info("[INFO] Memory pressure detected during pattern retrieval")
            
            if min_similarity is None:
                min_similarity = self.similarity_threshold
            
            matches = []
            current_hash = self._create_pattern_hash(current_conditions, strategy, "")
            
            # OPTIMIZED: Limit search to prevent memory issues
            max_search_items = min(1000, len(self.memories))
            memory_items = list(self.memories.items())[:max_search_items]
            
            # Search through memories (optimized)
            for memory_id, memory in memory_items:
                try:
                    similarity = self._calculate_similarity(current_conditions, memory.market_conditions)
                    
                    if similarity >= min_similarity:
                        # Calculate confidence based on historical success
                        confidence = self._calculate_match_confidence(memory, similarity)
                        
                        # Determine recommended action
                        recommended_action = self._get_recommended_action(memory, current_conditions)
                        
                        # Assess risk
                        risk_assessment = self._assess_pattern_risk(memory, current_conditions)
                        
                        match = PatternMatch(
                            memory=memory,
                            similarity_score=similarity,
                            confidence=confidence,
                            recommended_action=recommended_action,
                            risk_assessment=risk_assessment
                        )
                        
                        matches.append(match)
                        
                        # Update memory usage
                        memory.usage_count += 1
                        memory.last_accessed = datetime.utcnow()
                        
                        # OPTIMIZATION: Limit matches to prevent memory overload
                        if len(matches) >= 50:
                            break
                            
                except Exception as e:
                    # Skip problematic memory entries
                    continue
            
            # Sort by confidence and similarity
            matches.sort(key=lambda x: (x.confidence, x.similarity_score), reverse=True)
            
            # Return top matches only
            top_matches = matches[:10]
            
            self.logger.info(f"[OK] Found {len(top_matches)} similar patterns (min similarity: {min_similarity:.2f})")
            
            return top_matches
            
        except Exception as e:
            self.logger.error(f"Error retrieving similar patterns: {e}")
            return []
    
    async def update_strategy_memory(self, strategy_name: str, parameters: Dict[str, Any], performance: Dict[str, Any]):
        """Update strategy-specific memory"""
        try:
            if strategy_name not in self.strategy_memory:
                self.strategy_memory[strategy_name] = {
                    'best_parameters': parameters.copy(),
                    'best_performance': performance.copy(),
                    'parameter_history': [],
                    'performance_history': []
                }
            
            strategy_mem = self.strategy_memory[strategy_name]
            
            # Add to history
            strategy_mem['parameter_history'].append({
                'timestamp': datetime.utcnow(),
                'parameters': parameters.copy(),
                'performance': performance.copy()
            })
            
            # Keep only recent history
            if len(strategy_mem['parameter_history']) > 100:
                strategy_mem['parameter_history'] = strategy_mem['parameter_history'][-100:]
            
            # Update best if this performance is better
            current_score = performance.get('profit_factor', 0) * performance.get('win_rate', 0)
            best_score = strategy_mem['best_performance'].get('profit_factor', 0) * strategy_mem['best_performance'].get('win_rate', 0)
            
            if current_score > best_score:
                strategy_mem['best_parameters'] = parameters.copy()
                strategy_mem['best_performance'] = performance.copy()
                
                self.logger.info(f"PERFORMANCE: Updated best parameters for {strategy_name} (score: {current_score:.3f})")
            
            # Save to database
            await self._save_strategy_memory(strategy_name, strategy_mem)
            
        except Exception as e:
            self.logger.error(f"Error updating strategy memory: {e}")
    
    async def learn_from_outcome(self, decision_context: Dict[str, Any], outcome: Dict[str, Any]):
        """Learn from trading outcomes and update patterns"""
        try:
            # Create experience record
            experience_id = await self.store_trading_experience(
                market_conditions=decision_context.get('market_conditions', {}),
                strategy_used=decision_context.get('strategy', ''),
                action_taken=decision_context.get('action', ''),
                outcome=outcome,
                context=decision_context
            )
            
            # Analyze patterns for improvement
            await self._analyze_and_improve_patterns(decision_context, outcome)
            
            self.logger.info(f"LEARNING: Learned from outcome: {experience_id}")
            
        except Exception as e:
            self.logger.error(f"Error learning from outcome: {e}")
    
    async def get_recommendations(self, current_context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Get AI recommendations based on current context and past experiences"""
        try:
            # Get similar patterns
            similar_patterns = await self.retrieve_similar_patterns(
                current_context.get('market_conditions', {}),
                current_context.get('strategy', ''),
                min_similarity=self.similarity_threshold
            )
            
            recommendations = []
            for pattern in similar_patterns:
                recommendation = {
                    'action': pattern.recommended_action,
                    'confidence': pattern.confidence,
                    'similarity': pattern.similarity_score,
                    'risk_level': pattern.risk_assessment.get('risk_level', 'medium'),
                    'expected_outcome': pattern.memory.outcome,
                    'reasoning': f"Based on {pattern.memory.usage_count} similar past experiences",
                    'memory_id': pattern.memory.memory_id
                }
                recommendations.append(recommendation)
            
            # Sort by confidence and relevance
            recommendations.sort(key=lambda x: (x['confidence'], x['similarity']), reverse=True)
            
            return recommendations[:5]  # Top 5 recommendations
            
        except Exception as e:
            self.logger.error(f"Error getting recommendations: {e}")
            return []
    
    async def adapt_strategy_parameters(self, strategy_name: str, current_performance: Dict[str, Any]) -> Dict[str, Any]:
        """Adapt strategy parameters based on learned experiences"""
        try:
            if strategy_name not in self.strategy_memory:
                return {}
            
            strategy_mem = self.strategy_memory[strategy_name]
            best_params = strategy_mem['best_parameters'].copy()
            
            # Analyze recent performance trends
            recent_history = strategy_mem['parameter_history'][-10:]  # Last 10 records
            
            if len(recent_history) < 3:
                return best_params
            
            # Calculate performance trend
            recent_scores = []
            for record in recent_history:
                perf = record['performance']
                score = perf.get('profit_factor', 0) * perf.get('win_rate', 0)
                recent_scores.append(score)
            
            # If performance is declining, suggest parameter adjustments
            if len(recent_scores) >= 3:
                trend = np.polyfit(range(len(recent_scores)), recent_scores, 1)[0]
                
                if trend < -0.01:  # Declining performance
                    # Suggest more conservative parameters
                    adjusted_params = best_params.copy()
                    for key, value in adjusted_params.items():
                        if 'risk' in key.lower():
                            adjusted_params[key] = value * 0.8  # Reduce risk
                        elif 'size' in key.lower():
                            adjusted_params[key] = value * 0.9  # Smaller positions
                    
                    self.logger.info(f"📉 Suggesting conservative parameters for {strategy_name}")
                    return adjusted_params
            
            return best_params
            
        except Exception as e:
            self.logger.error(f"Error adapting strategy parameters: {e}")
            return {}
    
    # Private helper methods
    async def _save_memory_to_database(self, memory: TradingMemory):
        """Save memory to database"""
        try:
            await self.db.execute(
                """
                INSERT INTO trading_memories 
                (memory_id, pattern_type, importance, timestamp, market_conditions, 
                 strategy_used, action_taken, outcome, context, pattern_hash, 
                 confidence, usage_count, success_rate)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
                """,
                memory.memory_id, memory.pattern_type.value, memory.importance.value,
                memory.timestamp, json.dumps(memory.market_conditions),
                memory.strategy_used, memory.action_taken, json.dumps(memory.outcome),
                json.dumps(memory.context), memory.pattern_hash, memory.confidence,
                memory.usage_count, memory.success_rate
            )
        except Exception as e:
            self.logger.error(f"Error saving memory to database: {e}")
    
    async def _save_strategy_memory(self, strategy_name: str, strategy_mem: Dict[str, Any]):
        """Save strategy memory to database"""
        try:
            await self.db.execute(
                """
                INSERT INTO strategy_memories (strategy_name, memory_data, updated_at)
                VALUES ($1, $2, $3)
                ON CONFLICT (strategy_name) 
                DO UPDATE SET memory_data = $2, updated_at = $3
                """,
                strategy_name, json.dumps(strategy_mem), datetime.utcnow()
            )
        except Exception as e:
            self.logger.error(f"Error saving strategy memory: {e}")
    
    async def _load_memories_from_database(self):
        """Load existing memories from database"""
        try:
            rows = await self.db.fetch_all(
                "SELECT * FROM trading_memories ORDER BY timestamp DESC LIMIT 1000"
            )
            
            for row in rows:
                memory = TradingMemory(
                    memory_id=row['memory_id'],
                    pattern_type=PatternType(row['pattern_type']),
                    importance=MemoryImportance(row['importance']),
                    timestamp=row['timestamp'],
                    market_conditions=json.loads(row['market_conditions']),
                    strategy_used=row['strategy_used'],
                    action_taken=row['action_taken'],
                    outcome=json.loads(row['outcome']),
                    context=json.loads(row['context']),
                    pattern_hash=row['pattern_hash'],
                    confidence=row['confidence'],
                    usage_count=row['usage_count'],
                    last_accessed=row['timestamp'],
                    success_rate=row['success_rate']
                )
                self.memories[memory.memory_id] = memory
                
        except Exception as e:
            self.logger.error(f"Error loading memories from database: {e}")
    
    async def _build_pattern_index(self):
        """Build pattern index for fast similarity search"""
        try:
            for memory_id, memory in self.memories.items():
                self.pattern_index[memory.pattern_hash].append(memory_id)
        except Exception as e:
            self.logger.error(f"Error building pattern index: {e}")
    
    async def _load_strategy_memories(self):
        """Load strategy memories from database"""
        try:
            rows = await self.db.fetch_all("SELECT * FROM strategy_memories")
            
            for row in rows:
                strategy_name = row['strategy_name']
                memory_data = json.loads(row['memory_data'])
                self.strategy_memory[strategy_name] = memory_data
                
        except Exception as e:
            self.logger.error(f"Error loading strategy memories: {e}")
    
    def _create_pattern_hash(self, market_conditions: Dict[str, Any], strategy: str, action: str) -> str:
        """Create a hash for pattern matching"""
        try:
            # Extract key features for hashing
            features = {
                'volatility': market_conditions.get('volatility', 0),
                'trend': market_conditions.get('trend_direction', 'neutral'),
                'volume': market_conditions.get('volume_normalized', 0),
                'rsi': market_conditions.get('rsi', 50),
                'strategy': strategy,
                'action_type': action.split('_')[0] if '_' in action else action
            }
            
            # Create hash
            feature_string = json.dumps(features, sort_keys=True)
            return hashlib.md5(feature_string.encode()).hexdigest()
            
        except Exception as e:
            self.logger.error(f"Error creating pattern hash: {e}")
            return ""
    
    def _determine_importance(self, outcome: Dict[str, Any]) -> MemoryImportance:
        """Determine importance of a trading outcome"""
        try:
            pnl = outcome.get('pnl', 0)
            success = outcome.get('success', False)
            
            if abs(pnl) > 1000:  # Large PnL
                return MemoryImportance.CRITICAL
            elif abs(pnl) > 500:
                return MemoryImportance.HIGH
            elif success:
                return MemoryImportance.MEDIUM
            else:
                return MemoryImportance.LOW
                
        except Exception:
            return MemoryImportance.LOW
    
    def _calculate_confidence(self, pattern_hash: str, outcome: Dict[str, Any]) -> float:
        """Calculate confidence based on historical pattern performance"""
        try:
            pattern_perf = self.pattern_performance[pattern_hash]
            if pattern_perf['total'] == 0:
                return 0.5  # Default confidence for new patterns
            
            success_rate = pattern_perf['success'] / pattern_perf['total']
            return min(0.95, max(0.05, success_rate))
            
        except Exception:
            return 0.5
    
    def _classify_pattern_type(self, market_conditions: Dict[str, Any], action: str) -> PatternType:
        """Classify the type of pattern"""
        try:
            if 'volatility' in market_conditions and market_conditions['volatility'] > 0.5:
                return PatternType.MARKET_CONDITION
            elif 'volume' in market_conditions:
                return PatternType.VOLUME_PATTERN
            elif 'trend' in str(action).lower():
                return PatternType.PRICE_PATTERN
            else:
                return PatternType.STRATEGY_PERFORMANCE
                
        except Exception:
            return PatternType.STRATEGY_PERFORMANCE
    
    def _calculate_similarity(self, conditions1: Dict[str, Any], conditions2: Dict[str, Any]) -> float:
        """Calculate similarity between market conditions"""
        try:
            if not conditions1 or not conditions2:
                return 0.0
            
            # Extract common features
            features1 = {}
            features2 = {}
            
            for key in self.feature_weights:
                features1[key] = conditions1.get(key, 0)
                features2[key] = conditions2.get(key, 0)
            
            # Calculate weighted similarity
            total_similarity = 0.0
            total_weight = 0.0
            
            for key, weight in self.feature_weights.items():
                val1 = features1.get(key, 0)
                val2 = features2.get(key, 0)
                
                # Normalize and calculate similarity
                if isinstance(val1, (int, float)) and isinstance(val2, (int, float)):
                    max_val = max(abs(val1), abs(val2), 1)
                    similarity = 1.0 - abs(val1 - val2) / max_val
                    total_similarity += similarity * weight
                    total_weight += weight
                elif val1 == val2:
                    total_similarity += weight
                    total_weight += weight
            
            return total_similarity / total_weight if total_weight > 0 else 0.0
            
        except Exception as e:
            self.logger.error(f"Error calculating similarity: {e}")
            return 0.0
    
    def _calculate_match_confidence(self, memory: TradingMemory, similarity: float) -> float:
        """Calculate confidence for a pattern match"""
        try:
            base_confidence = memory.confidence
            usage_bonus = min(0.2, memory.usage_count * 0.01)
            similarity_factor = similarity
            
            return min(0.95, base_confidence * similarity_factor + usage_bonus)
            
        except Exception:
            return 0.5
    
    def _get_recommended_action(self, memory: TradingMemory, current_conditions: Dict[str, Any]) -> str:
        """Get recommended action based on memory"""
        try:
            if memory.outcome.get('success', False):
                return memory.action_taken
            else:
                # Suggest opposite or modified action
                action = memory.action_taken
                if 'buy' in action.lower():
                    return action.replace('buy', 'sell')
                elif 'sell' in action.lower():
                    return action.replace('sell', 'buy')
                else:
                    return 'hold'
                    
        except Exception:
            return 'hold'
    
    def _assess_pattern_risk(self, memory: TradingMemory, current_conditions: Dict[str, Any]) -> Dict[str, Any]:
        """Assess risk for a pattern match"""
        try:
            historical_pnl = memory.outcome.get('pnl', 0)
            success_rate = memory.success_rate
            
            if success_rate > 0.8:
                risk_level = 'low'
            elif success_rate > 0.6:
                risk_level = 'medium'
            else:
                risk_level = 'high'
            
            return {
                'risk_level': risk_level,
                'historical_pnl': historical_pnl,
                'success_rate': success_rate,
                'recommendation': 'proceed' if risk_level != 'high' else 'caution'
            }
            
        except Exception:
            return {'risk_level': 'high', 'recommendation': 'caution'}
    
    def _update_pattern_performance(self, pattern_hash: str, success: bool):
        """Update pattern performance statistics"""
        try:
            self.pattern_performance[pattern_hash]['total'] += 1
            if success:
                self.pattern_performance[pattern_hash]['success'] += 1
        except Exception as e:
            self.logger.error(f"Error updating pattern performance: {e}")
    
    async def _analyze_and_improve_patterns(self, decision_context: Dict[str, Any], outcome: Dict[str, Any]):
        """Analyze outcomes and improve pattern recognition"""
        try:
            # This is where advanced AI learning would happen
            # For now, we update basic statistics
            pattern_hash = self._create_pattern_hash(
                decision_context.get('market_conditions', {}),
                decision_context.get('strategy', ''),
                decision_context.get('action', '')
            )
            
            success = outcome.get('success', False)
            self._update_pattern_performance(pattern_hash, success)
            
        except Exception as e:
            self.logger.error(f"Error analyzing patterns: {e}")
    
    async def periodic_cleanup(self):
        """Perform periodic memory cleanup"""
        try:
            now = datetime.utcnow()
            
            # Check if cleanup is needed
            if (now - self.last_cleanup).total_seconds() < self.cleanup_interval:
                return
            
            self.logger.info("[INFO] Starting periodic memory cleanup...")
            
            # Perform cleanup
            self._perform_memory_cleanup()
            
            # Update last cleanup time
            self.last_cleanup = now
            
            # Log memory status
            import psutil
            memory_usage = psutil.virtual_memory().percent
            self.logger.info(f"[OK] Memory cleanup completed - Current usage: {memory_usage:.1f}%")
            
        except Exception as e:
            self.logger.error(f"Error during periodic cleanup: {e}")
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """Get memory manager statistics"""
        try:
            import psutil
            
            return {
                'total_memories': len(self.memories),
                'recent_memories': len(self.recent_memories),
                'pattern_index_size': len(self.pattern_index),
                'strategy_memories': len(self.strategy_memory),
                'system_memory_usage': psutil.virtual_memory().percent,
                'last_cleanup': self.last_cleanup.isoformat(),
                'max_memories': self.max_memories
            }
            
        except Exception as e:
            self.logger.error(f"Error getting memory stats: {e}")
            return {}
    
    def shutdown(self):
        """Shutdown the memory manager"""
        try:
            self.logger.info("Shutting down Persistent Memory Manager...")
            # Save any pending memories
            if hasattr(self, 'memories') and self.memories:
                self.save_memories()
            self.logger.info("Persistent Memory Manager shutdown complete")
        except Exception as e:
            self.logger.error(f"Error during memory manager shutdown: {e}")


# Alias for backward compatibility and simpler imports
MemoryManager = PersistentMemoryManager

# Make available for import
__all__ = ['PersistentMemoryManager', 'MemoryManager', 'TradingMemory', 'PatternMatch', 'PatternType', 'MemoryImportance']