"""
Social Sentiment Crawler
Collects and analyzes sentiment from social media platforms
"""

import asyncio
import logging
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional
# Make social media imports optional
try:
    import tweepy
    TWEEPY_AVAILABLE = True
except ImportError:
    TWEEPY_AVAILABLE = False
    class tweepy:
        class API:
            def __init__(self, *args, **kwargs): pass
            def search_tweets(self, *args, **kwargs): return []
        class OAuthHandler:
            def __init__(self, *args): pass
            def set_access_token(self, *args): pass
        class Client:
            def __init__(self, *args, **kwargs): pass
            def search_recent_tweets(self, *args, **kwargs): return type('obj', (object,), {'data': []})()
        class Paginator:
            def __init__(self, *args, **kwargs): pass
            def flatten(self): return []

try:
    import praw
    PRAW_AVAILABLE = True
except ImportError:
    PRAW_AVAILABLE = False
    class praw:
        class Reddit:
            def __init__(self, *args, **kwargs): pass
            def subreddit(self, name): return type('obj', (object,), {'hot': lambda limit=10: []})()

try:
    from vaderSentiment.vaderSentiment import SentimentIntensityAnalyzer
    VADER_AVAILABLE = True
except ImportError:
    VADER_AVAILABLE = False
    class SentimentIntensityAnalyzer:
        def polarity_scores(self, text): return {'compound': 0.0, 'pos': 0.0, 'neu': 1.0, 'neg': 0.0}

if not all([TWEEPY_AVAILABLE, PRAW_AVAILABLE, VADER_AVAILABLE]):
    print("[WARNING] Some social sentiment analysis dependencies not available - using fallbacks")

from ..core.config import BotConfig
from ..database.connection import DatabaseManager


class SocialSentimentCrawler:
    """
    Advanced social sentiment crawler that:
    - Monitors Twitter for crypto mentions
    - Tracks Reddit discussions
    - Analyzes social media sentiment
    - Identifies trending topics and influencers
    """
    
    def __init__(self, config: BotConfig, db_manager: DatabaseManager):
        self.config = config
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)
        self.running = False
        
        # Initialize sentiment analyzer
        self.vader_analyzer = SentimentIntensityAnalyzer()
        
        # Initialize social media clients
        self.twitter_client = self._initialize_twitter()
        self.reddit_client = self._initialize_reddit()
        
        # Keywords to track - handle config structure safely
        base_keywords = []
        try:
            if hasattr(self.config, 'data_crawler') and hasattr(self.config.data_crawler, 'social_keywords'):
                base_keywords = self.config.data_crawler.social_keywords
            elif hasattr(self.config, 'data_crawler') and isinstance(self.config.data_crawler, dict):
                base_keywords = self.config.data_crawler.get('social_keywords', [])
            else:
                base_keywords = []
        except (AttributeError, TypeError):
            base_keywords = []
            
        self.crypto_keywords = base_keywords + [
            '$BTC', '$ETH', '$ADA', '$SOL', '$DOT',
            'hodl', 'moon', 'diamond hands', 'paper hands',
            'bull market', 'bear market', 'altseason',
            'to the moon', 'buy the dip', 'rekt'
        ]
        
        self.crawl_tasks = []
        
    def _initialize_twitter(self) -> Optional[tweepy.Client]:
        """Initialize Twitter API client with live credentials"""
        try:
            # Get Twitter credentials from environment or config
            import os
            bearer_token = os.getenv('TWITTER_BEARER_TOKEN')
            api_key = os.getenv('TWITTER_API_KEY')
            api_secret = os.getenv('TWITTER_API_SECRET')
            access_token = os.getenv('TWITTER_ACCESS_TOKEN')
            access_token_secret = os.getenv('TWITTER_ACCESS_TOKEN_SECRET')
            
            if not bearer_token:
                # Try fallback from config
                try:
                    twitter_config = self.config.get_api_key("twitter")
                    bearer_token = twitter_config.get("bearer_token")
                except:
                    pass
            
            if not bearer_token:
                self.logger.warning("Twitter bearer token not configured")
                return None
            
            # Initialize with bearer token for v2 API
            client = tweepy.Client(
                bearer_token=bearer_token,
                consumer_key=api_key,
                consumer_secret=api_secret,
                access_token=access_token,
                access_token_secret=access_token_secret,
                wait_on_rate_limit=True
            )
            
            self.logger.info("[TWITTER] Twitter API client initialized successfully")
            return client
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Twitter client: {e}")
            return None
    
    def _initialize_reddit(self) -> Optional[praw.Reddit]:
        """Initialize Reddit API client with live credentials"""
        try:
            # Get Reddit credentials from environment or config
            import os
            client_id = os.getenv('REDDIT_CLIENT_ID')
            client_secret = os.getenv('REDDIT_CLIENT_SECRET')
            user_agent = os.getenv('REDDIT_USER_AGENT', 'BybitTradingBot/1.0')
            
            if not client_id or not client_secret:
                # Try fallback from config
                try:
                    reddit_config = self.config.get_api_key("reddit")
                    client_id = reddit_config.get("client_id")
                    client_secret = reddit_config.get("client_secret")
                    user_agent = reddit_config.get("user_agent", "BybitBot/1.0")
                except:
                    pass
            
            if not client_id or not client_secret:
                self.logger.warning("Reddit API credentials not configured")
                return None
            
            reddit_client = praw.Reddit(
                client_id=client_id,
                client_secret=client_secret,
                user_agent=user_agent
            )
            
            self.logger.info("[REDDIT] Reddit API client initialized successfully")
            return reddit_client
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Reddit client: {e}")
            return None
    
    async def start(self):
        """Start the social sentiment crawler"""
        if self.running:
            return
            
        self.running = True
        self.logger.info("📱 Starting social sentiment crawler...")
        
        # Start crawling tasks
        self.crawl_tasks = []
        
        if self.twitter_client:
            self.crawl_tasks.append(asyncio.create_task(self._crawl_twitter()))
        
        if self.reddit_client:
            self.crawl_tasks.append(asyncio.create_task(self._crawl_reddit()))
        
        self.crawl_tasks.extend([
            asyncio.create_task(self._analyze_sentiment_trends()),
            asyncio.create_task(self._track_influencers()),
            asyncio.create_task(self._calculate_social_metrics()),
        ])
        
        if self.crawl_tasks:
            await asyncio.gather(*self.crawl_tasks, return_exceptions=True)
    
    async def stop(self):
        """Stop the social sentiment crawler"""
        self.running = False
        
        # Cancel all tasks
        for task in self.crawl_tasks:
            task.cancel()
            
        self.logger.info("🛑 Social sentiment crawler stopped")
    
    async def _crawl_twitter(self):
        """Crawl Twitter for crypto-related tweets"""
        while self.running:
            try:
                for keyword in self.crypto_keywords[:5]:  # Rotate keywords to avoid rate limits
                    try:
                        # Search for recent tweets
                        tweets = tweepy.Paginator(
                            self.twitter_client.search_recent_tweets,
                            query=f"{keyword} -is:retweet lang:en",
                            max_results=50,
                            tweet_fields=['created_at', 'author_id', 'public_metrics', 'context_annotations']
                        ).flatten(limit=100)
                        
                        for tweet in tweets:
                            await self._process_twitter_post(tweet, keyword)
                        
                    except Exception as e:
                        self.logger.error(f"Error fetching tweets for {keyword}: {e}")
                    
                    await asyncio.sleep(60)  # Wait between keyword searches
                
                await asyncio.sleep(self.config.data_crawler.social_sentiment_interval)
                
            except Exception as e:
                self.logger.error(f"Error in Twitter crawler: {e}")
                await asyncio.sleep(300)
    
    async def _crawl_reddit(self):
        """Crawl Reddit for crypto discussions"""
        while self.running:
            try:
                # Define crypto subreddits to monitor
                crypto_subreddits = [
                    'cryptocurrency', 'bitcoin', 'ethereum', 'cardano',
                    'solana', 'polkadot', 'defi', 'cryptomarkets',
                    'trading', 'bitcoinmarkets'
                ]
                
                for subreddit_name in crypto_subreddits:
                    try:
                        subreddit = self.reddit_client.subreddit(subreddit_name)
                        
                        # Get hot posts
                        for post in subreddit.hot(limit=25):
                            await self._process_reddit_post(post, subreddit_name)
                        
                        # Get new posts
                        for post in subreddit.new(limit=25):
                            await self._process_reddit_post(post, subreddit_name)
                        
                    except Exception as e:
                        self.logger.error(f"Error fetching from r/{subreddit_name}: {e}")
                    
                    await asyncio.sleep(30)  # Wait between subreddits
                
                await asyncio.sleep(self.config.data_crawler.social_sentiment_interval * 2)
                
            except Exception as e:
                self.logger.error(f"Error in Reddit crawler: {e}")
                await asyncio.sleep(300)
    
    async def _analyze_sentiment_trends(self):
        """Analyze sentiment trends over time"""
        while self.running:
            try:
                # Analyze hourly sentiment trends
                cutoff_time = datetime.utcnow() - timedelta(hours=24)
                
                query = """
                SELECT 
                    DATE_TRUNC('hour', created_at) as hour,
                    platform,
                    keyword,
                    AVG(sentiment_compound) as avg_sentiment,
                    COUNT(*) as post_count,
                    AVG(engagement_score) as avg_engagement
                FROM social_sentiment 
                WHERE created_at > $1 
                GROUP BY hour, platform, keyword
                ORDER BY hour DESC
                """
                
                trends = await self.db_manager.fetch_all(query, cutoff_time)
                
                # Calculate trend scores
                for trend in trends:
                    trend_score = self._calculate_trend_score(
                        trend['avg_sentiment'],
                        trend['post_count'],
                        trend['avg_engagement']
                    )
                    
                    # Store trend analysis
                    await self._store_sentiment_trend({
                        'timestamp': trend['hour'],
                        'platform': trend['platform'],
                        'keyword': trend['keyword'],
                        'avg_sentiment': trend['avg_sentiment'],
                        'post_count': trend['post_count'],
                        'avg_engagement': trend['avg_engagement'],
                        'trend_score': trend_score
                    })
                
                await asyncio.sleep(3600)  # Analyze every hour
                
            except Exception as e:
                self.logger.error(f"Error analyzing sentiment trends: {e}")
                await asyncio.sleep(1800)
    
    async def _track_influencers(self):
        """Track influential accounts and their sentiment"""
        while self.running:
            try:
                # Identify influential accounts based on engagement
                cutoff_time = datetime.utcnow() - timedelta(hours=24)
                
                query = """
                SELECT 
                    author_id,
                    platform,
                    COUNT(*) as post_count,
                    AVG(engagement_score) as avg_engagement,
                    AVG(sentiment_compound) as avg_sentiment,
                    SUM(CASE WHEN engagement_score > 100 THEN 1 ELSE 0 END) as viral_posts
                FROM social_sentiment 
                WHERE created_at > $1 AND author_id IS NOT NULL
                GROUP BY author_id, platform
                HAVING COUNT(*) > 5 AND AVG(engagement_score) > 50
                ORDER BY avg_engagement DESC
                LIMIT 100
                """
                
                influencers = await self.db_manager.fetch_all(query, cutoff_time)
                
                # Store influencer data
                for influencer in influencers:
                    influence_score = self._calculate_influence_score(
                        influencer['post_count'],
                        influencer['avg_engagement'],
                        influencer['viral_posts']
                    )
                    
                    await self._store_influencer_data({
                        'timestamp': datetime.utcnow(),
                        'author_id': influencer['author_id'],
                        'platform': influencer['platform'],
                        'post_count': influencer['post_count'],
                        'avg_engagement': influencer['avg_engagement'],
                        'avg_sentiment': influencer['avg_sentiment'],
                        'viral_posts': influencer['viral_posts'],
                        'influence_score': influence_score
                    })
                
                await asyncio.sleep(7200)  # Update every 2 hours
                
            except Exception as e:
                self.logger.error(f"Error tracking influencers: {e}")
                await asyncio.sleep(3600)
    
    async def _calculate_social_metrics(self):
        """Calculate advanced social metrics"""
        while self.running:
            try:
                # Calculate metrics for each trading pair
                for symbol in self.config.get_trading_pairs():
                    symbol_keywords = [symbol.replace('USDT', ''), f"${symbol.replace('USDT', '')}"]
                    
                    cutoff_time = datetime.utcnow() - timedelta(hours=6)
                    
                    # Get social data for this symbol
                    query = """
                    SELECT * FROM social_sentiment 
                    WHERE created_at > $1 AND (
                        keyword = ANY($2) OR 
                        content ILIKE ANY(ARRAY['%' || keyword || '%' for keyword in $2])
                    )
                    """
                    
                    social_data = await self.db_manager.fetch_all(query, cutoff_time, symbol_keywords)
                    
                    if social_data:
                        metrics = self._calculate_symbol_social_metrics(symbol, social_data)
                        await self._store_social_metrics(metrics)
                
                await asyncio.sleep(1800)  # Update every 30 minutes
                
            except Exception as e:
                self.logger.error(f"Error calculating social metrics: {e}")
                await asyncio.sleep(900)
    
    async def _process_twitter_post(self, tweet, keyword: str):
        """Process a single Twitter post"""
        try:
            # Check if tweet already exists
            existing = await self._check_post_exists(tweet.id, 'twitter')
            if existing:
                return
            
            # Calculate engagement score
            metrics = tweet.public_metrics or {}
            engagement_score = (
                metrics.get('like_count', 0) * 1 +
                metrics.get('retweet_count', 0) * 3 +
                metrics.get('reply_count', 0) * 2 +
                metrics.get('quote_count', 0) * 2
            )
            
            # Analyze sentiment
            sentiment = self._analyze_post_sentiment(tweet.text)
            
            # Extract mentions of crypto symbols
            crypto_mentions = self._extract_crypto_mentions(tweet.text)
            
            # Store social data
            social_data = {
                'post_id': str(tweet.id),
                'platform': 'twitter',
                'author_id': str(tweet.author_id),
                'content': tweet.text,
                'created_at': tweet.created_at,
                'keyword': keyword,
                'sentiment_compound': sentiment['compound'],
                'sentiment_positive': sentiment['pos'],
                'sentiment_negative': sentiment['neg'],
                'sentiment_neutral': sentiment['neu'],
                'engagement_score': engagement_score,
                'crypto_mentions': ','.join(crypto_mentions),
                'timestamp': datetime.utcnow()
            }
            
            await self._store_social_data(social_data)
            
        except Exception as e:
            self.logger.error(f"Error processing Twitter post {tweet.id}: {e}")
    
    async def _process_reddit_post(self, post, subreddit: str):
        """Process a single Reddit post"""
        try:
            # Check if post already exists
            existing = await self._check_post_exists(post.id, 'reddit')
            if existing:
                return
            
            # Calculate engagement score
            engagement_score = post.score + post.num_comments * 2
            
            # Combine title and text for analysis
            content = f"{post.title} {getattr(post, 'selftext', '')}"
            
            # Analyze sentiment
            sentiment = self._analyze_post_sentiment(content)
            
            # Extract crypto mentions
            crypto_mentions = self._extract_crypto_mentions(content)
            
            # Determine primary keyword
            primary_keyword = self._determine_primary_keyword(content)
            
            # Store social data
            social_data = {
                'post_id': post.id,
                'platform': 'reddit',
                'author_id': str(post.author) if post.author else 'unknown',
                'content': content[:5000],  # Limit content length
                'created_at': datetime.fromtimestamp(post.created_utc),
                'keyword': primary_keyword or subreddit,
                'sentiment_compound': sentiment['compound'],
                'sentiment_positive': sentiment['pos'],
                'sentiment_negative': sentiment['neg'],
                'sentiment_neutral': sentiment['neu'],
                'engagement_score': engagement_score,
                'crypto_mentions': ','.join(crypto_mentions),
                'metadata': f"subreddit:{subreddit},upvotes:{post.score},comments:{post.num_comments}",
                'timestamp': datetime.utcnow()
            }
            
            await self._store_social_data(social_data)
            
        except Exception as e:
            self.logger.error(f"Error processing Reddit post {post.id}: {e}")
    
    def _analyze_post_sentiment(self, text: str) -> Dict:
        """Analyze sentiment of social media post"""
        # Clean text
        clean_text = re.sub(r'http\S+|www.\S+|https\S+', '', text, flags=re.MULTILINE)
        clean_text = re.sub(r'@\w+|#\w+', '', clean_text)
        
        # VADER sentiment analysis (good for social media)
        vader_scores = self.vader_analyzer.polarity_scores(clean_text)
        
        return vader_scores
    
    def _extract_crypto_mentions(self, text: str) -> List[str]:
        """Extract cryptocurrency mentions from text"""
        text_lower = text.lower()
        mentions = []
        
        # Define crypto patterns
        crypto_patterns = {
            'bitcoin': ['bitcoin', 'btc', '$btc'],
            'ethereum': ['ethereum', 'eth', '$eth'],
            'cardano': ['cardano', 'ada', '$ada'],
            'solana': ['solana', 'sol', '$sol'],
            'polkadot': ['polkadot', 'dot', '$dot'],
        }
        
        for crypto, patterns in crypto_patterns.items():
            for pattern in patterns:
                if pattern in text_lower:
                    mentions.append(crypto)
                    break
        
        return mentions
    
    def _determine_primary_keyword(self, text: str) -> Optional[str]:
        """Determine the primary crypto keyword in text"""
        text_lower = text.lower()
        
        # Count occurrences of each keyword
        keyword_counts = {}
        for keyword in self.crypto_keywords:
            count = text_lower.count(keyword.lower())
            if count > 0:
                keyword_counts[keyword] = count
        
        if keyword_counts:
            return max(keyword_counts, key=keyword_counts.get)
        
        return None
    
    def _calculate_trend_score(self, avg_sentiment: float, post_count: int, avg_engagement: float) -> float:
        """Calculate trend score based on sentiment, volume, and engagement"""
        # Normalize components
        sentiment_weight = abs(avg_sentiment) * 0.4
        volume_weight = min(post_count / 100, 1.0) * 0.3
        engagement_weight = min(avg_engagement / 1000, 1.0) * 0.3
        
        return sentiment_weight + volume_weight + engagement_weight
    
    def _calculate_influence_score(self, post_count: int, avg_engagement: float, viral_posts: int) -> float:
        """Calculate influence score for an account"""
        # Normalize and weight components
        volume_score = min(post_count / 50, 1.0) * 0.3
        engagement_score = min(avg_engagement / 500, 1.0) * 0.5
        viral_score = min(viral_posts / 10, 1.0) * 0.2
        
        return volume_score + engagement_score + viral_score
    
    def _calculate_symbol_social_metrics(self, symbol: str, social_data: List) -> Dict:
        """Calculate social metrics for a trading symbol"""
        if not social_data:
            return {}
        
        # Convert to easier format
        posts = [dict(post) for post in social_data]
        
        # Calculate metrics
        total_posts = len(posts)
        avg_sentiment = sum(p['sentiment_compound'] for p in posts) / total_posts
        total_engagement = sum(p['engagement_score'] for p in posts)
        
        # Platform distribution
        platform_counts = {}
        for post in posts:
            platform = post['platform']
            platform_counts[platform] = platform_counts.get(platform, 0) + 1
        
        # Time-based analysis
        sentiment_by_hour = {}
        for post in posts:
            hour = post['created_at'].replace(minute=0, second=0, microsecond=0)
            if hour not in sentiment_by_hour:
                sentiment_by_hour[hour] = []
            sentiment_by_hour[hour].append(post['sentiment_compound'])
        
        # Calculate sentiment momentum
        hours = sorted(sentiment_by_hour.keys())
        if len(hours) >= 2:
            recent_sentiment = sum(sentiment_by_hour[hours[-1]]) / len(sentiment_by_hour[hours[-1]])
            earlier_sentiment = sum(sentiment_by_hour[hours[-2]]) / len(sentiment_by_hour[hours[-2]])
            sentiment_momentum = recent_sentiment - earlier_sentiment
        else:
            sentiment_momentum = 0
        
        return {
            'timestamp': datetime.utcnow(),
            'symbol': symbol,
            'total_posts': total_posts,
            'avg_sentiment': avg_sentiment,
            'total_engagement': total_engagement,
            'sentiment_momentum': sentiment_momentum,
            'platform_distribution': platform_counts,
            'timeframe_hours': 6
        }
    
    async def _check_post_exists(self, post_id: str, platform: str) -> bool:
        """Check if social media post already exists"""
        query = "SELECT id FROM social_sentiment WHERE post_id = $1 AND platform = $2"
        result = await self.db_manager.fetch_one(query, post_id, platform)
        return result is not None
    
    async def _store_social_data(self, data: Dict):
        """Store social media data in database"""
        query = """
        INSERT INTO social_sentiment (post_id, platform, author_id, content, created_at, keyword,
                                    sentiment_compound, sentiment_positive, sentiment_negative,
                                    sentiment_neutral, engagement_score, crypto_mentions, metadata, timestamp)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
        ON CONFLICT (post_id, platform) DO NOTHING
        """
        
        await self.db_manager.execute(
            query,
            data['post_id'], data['platform'], data['author_id'], data['content'],
            data['created_at'], data['keyword'], data['sentiment_compound'],
            data['sentiment_positive'], data['sentiment_negative'], data['sentiment_neutral'],
            data['engagement_score'], data['crypto_mentions'], data.get('metadata', ''),
            data['timestamp']
        )
    
    async def _store_sentiment_trend(self, data: Dict):
        """Store sentiment trend analysis"""
        query = """
        INSERT INTO social_sentiment_trends (timestamp, platform, keyword, avg_sentiment,
                                           post_count, avg_engagement, trend_score)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        ON CONFLICT (timestamp, platform, keyword) DO UPDATE SET
        avg_sentiment = $4, post_count = $5, avg_engagement = $6, trend_score = $7
        """
        
        await self.db_manager.execute(
            query,
            data['timestamp'], data['platform'], data['keyword'], data['avg_sentiment'],
            data['post_count'], data['avg_engagement'], data['trend_score']
        )
    
    async def _store_influencer_data(self, data: Dict):
        """Store influencer analysis data"""
        query = """
        INSERT INTO social_influencers (timestamp, author_id, platform, post_count,
                                      avg_engagement, avg_sentiment, viral_posts, influence_score)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        ON CONFLICT (timestamp, author_id, platform) DO UPDATE SET
        post_count = $4, avg_engagement = $5, avg_sentiment = $6, viral_posts = $7, influence_score = $8
        """
        
        await self.db_manager.execute(
            query,
            data['timestamp'], data['author_id'], data['platform'], data['post_count'],
            data['avg_engagement'], data['avg_sentiment'], data['viral_posts'], data['influence_score']
        )
    
    async def _store_social_metrics(self, data: Dict):
        """Store social metrics for trading symbols"""
        if not data:
            return
            
        query = """
        INSERT INTO social_symbol_metrics (timestamp, symbol, total_posts, avg_sentiment,
                                         total_engagement, sentiment_momentum, timeframe_hours)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        """
        
        await self.db_manager.execute(
            query,
            data['timestamp'], data['symbol'], data['total_posts'], data['avg_sentiment'],
            data['total_engagement'], data['sentiment_momentum'], data['timeframe_hours']
        )
    
    async def get_social_sentiment_summary(self, symbol: str = None, hours: int = 24) -> Dict:
        """Get social sentiment summary"""
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        
        if symbol:
            query = """
            SELECT 
                COUNT(*) as total_posts,
                AVG(sentiment_compound) as avg_sentiment,
                SUM(engagement_score) as total_engagement,
                platform
            FROM social_sentiment 
            WHERE created_at > $1 AND (keyword ILIKE $2 OR crypto_mentions ILIKE $2)
            GROUP BY platform
            """
            results = await self.db_manager.fetch_all(query, cutoff_time, f"%{symbol.replace('USDT', '')}%")
        else:
            query = """
            SELECT 
                COUNT(*) as total_posts,
                AVG(sentiment_compound) as avg_sentiment,
                SUM(engagement_score) as total_engagement,
                platform
            FROM social_sentiment 
            WHERE created_at > $1
            GROUP BY platform
            """
            results = await self.db_manager.fetch_all(query, cutoff_time)
        
        return {
            'platform_summary': [dict(row) for row in results],
            'timeframe_hours': hours,
            'symbol': symbol
        }
    
    async def get_trending_social_topics(self, limit: int = 10) -> List[Dict]:
        """Get trending topics from social media"""
        cutoff_time = datetime.utcnow() - timedelta(hours=6)
        
        query = """
        SELECT 
            keyword,
            COUNT(*) as mention_count,
            AVG(sentiment_compound) as avg_sentiment,
            SUM(engagement_score) as total_engagement
        FROM social_sentiment 
        WHERE created_at > $1
        GROUP BY keyword
        ORDER BY mention_count DESC, total_engagement DESC
        LIMIT $2
        """
        
        results = await self.db_manager.fetch_all(query, cutoff_time, limit)
        return [dict(row) for row in results]
    
    async def get_influencer_sentiment(self, limit: int = 20) -> List[Dict]:
        """Get sentiment from influential accounts"""
        cutoff_time = datetime.utcnow() - timedelta(hours=24)
        
        query = """
        SELECT 
            si.author_id,
            si.platform,
            si.influence_score,
            AVG(ss.sentiment_compound) as recent_sentiment,
            COUNT(ss.id) as recent_posts
        FROM social_influencers si
        JOIN social_sentiment ss ON si.author_id = ss.author_id AND si.platform = ss.platform
        WHERE ss.created_at > $1 AND si.timestamp > $1
        GROUP BY si.author_id, si.platform, si.influence_score
        ORDER BY si.influence_score DESC, recent_posts DESC
        LIMIT $2
        """
        
        results = await self.db_manager.fetch_all(query, cutoff_time, limit)
        return [dict(row) for row in results]

    async def initialize(self):
        """Initialize the social sentiment crawler"""
        try:
            logging.info("Initializing Social Sentiment Crawler...")
            
            # Test database connection
            if hasattr(self.db_manager, 'test_connection'):
                await self.db_manager.test_connection()
            
            # Initialize sentiment analyzer
            if not hasattr(self, 'vader_analyzer'):
                self.vader_analyzer = SentimentIntensityAnalyzer()
            
            # Initialize API clients (with graceful fallback)
            await self._initialize_api_clients()
            
            logging.info("SUCCESS: Social Sentiment Crawler initialized successfully")
            return True
            
        except Exception as e:
            logging.error(f"Failed to initialize Social Sentiment Crawler: {e}")
            return False

    async def _initialize_api_clients(self):
        """Initialize API clients with proper error handling"""
        try:
            # Twitter API initialization
            if hasattr(self.config, 'twitter_bearer_token') and self.config.twitter_bearer_token:
                self.twitter_api = tweepy.Client(bearer_token=self.config.twitter_bearer_token)
            else:
                logging.warning("Twitter bearer token not configured")
                self.twitter_api = None
            
            # Reddit API initialization
            if (hasattr(self.config, 'reddit_client_id') and 
                hasattr(self.config, 'reddit_client_secret') and
                self.config.reddit_client_id and self.config.reddit_client_secret):
                self.reddit = praw.Reddit(
                    client_id=self.config.reddit_client_id,
                    client_secret=self.config.reddit_client_secret,
                    user_agent="crypto_sentiment_bot"
                )
            else:
                logging.warning("Reddit API credentials not configured")
                self.reddit = None
                
        except Exception as e:
            logging.error(f"Error initializing API clients: {e}")
            self.twitter_api = None
            self.reddit = None
