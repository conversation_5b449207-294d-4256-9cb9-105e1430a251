"""
Advanced Risk Management Module for Bybit Trading Bot
Implements comprehensive risk controls, position sizing, and portfolio management
"""

from __future__ import annotations

# CRITICAL: Apply comprehensive sys.warnoptions fix FIRST
import sys
import os

# Apply sys.warnoptions fix before any other imports
if not hasattr(sys, 'warnoptions'):
    sys.warnoptions = []
sys.warnoptions = getattr(sys, 'warnoptions', [])

import warnings
# Apply comprehensive warning suppression
os.environ['PYTHONWARNINGS'] = 'ignore'
warnings.filterwarnings('ignore')
warnings.simplefilter('ignore')

import asyncio
import numpy as np
from datetime import datetime, timedelta
from typing import Any, Optional
from dataclasses import dataclass
from enum import Enum
import pandas as pd

from ..core.config import BotConfig
from ..core.logger import TradingBotLogger
from ..database.connection import DatabaseManager


class RiskLevel(Enum):
    """Risk levels for different market conditions"""
    VERY_LOW = "very_low"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    VERY_HIGH = "very_high"
    CRITICAL = "critical"


@dataclass
class RiskMetrics:
    """Risk metrics container"""
    current_drawdown: float
    peak_equity: float
    current_equity: float
    daily_pnl: float
    total_exposure: float
    open_positions: int
    risk_score: float
    risk_level: RiskLevel
    is_trading_allowed: bool
    max_position_size: float


@dataclass
class PositionRisk:
    """Position risk assessment"""
    symbol: str
    size: float
    risk_amount: float
    risk_percentage: float
    stop_loss: Optional[float]
    take_profit: Optional[float]
    expected_holding_time: Optional[int]
    confidence: float
    market_volatility: float


class AdvancedRiskManager:
    """
    Advanced risk management system that:
    - Monitors portfolio risk in real-time
    - Calculates position sizes based on risk
    - Implements emergency stop procedures
    - Adapts to market volatility
    - Tracks performance metrics
    - Uses Kelly Criterion for optimal sizing
    - Implements correlation-based risk adjustment
    """
    
    def __init__(self, config: BotConfig, database_manager: DatabaseManager, bybit_client):
        self.config = config
        self.db = database_manager
        
        # Initialize logger FIRST before any usage
        self.logger = TradingBotLogger(config)
        
        # CRITICAL: Validate and store bybit_client with stronger reference
        if bybit_client is None:
            raise ValueError("[CRITICAL] bybit_client cannot be None during AdvancedRiskManager initialization")
        # Preserve client reference with validation
        self.bybit_client = bybit_client
        if self.bybit_client is None:
            self.logger.error("[CRITICAL] bybit_client is None during initialization!")
        else:
            self.logger.info(f"[OK] bybit_client initialized: {type(self.bybit_client).__name__}")
        self._original_bybit_client = bybit_client  # Backup reference
        self._client_type = type(bybit_client).__name__  # Store type info
        
        self.logger.info(f"[DEBUG] AdvancedRiskManager initialized with bybit_client: {self._client_type}")
        
        # Core risk parameters - OPTIMIZED FOR MAXIMUM PROFIT GENERATION
        self.max_risk_per_trade = getattr(config, 'risk_per_trade', 0.25)  # 25% per trade for maximum profit
        self.max_drawdown = getattr(config, 'max_drawdown', 40.0) / 100  # 40% limit for aggressive profit maximization
        self.max_open_positions = getattr(config, 'max_open_positions', 8)  # More positions for profit opportunities
        self.min_order_size = getattr(config, 'min_order_size', 5.0)  # Smaller minimum for more trades
        self.max_daily_loss = getattr(config, 'max_daily_loss', 1000.0)  # Higher daily loss limit for profit maximization
        
        # PRODUCTION PROFIT MAXIMIZATION PARAMETERS
        self.profit_optimization_mode = True
        self.aggressive_position_sizing = True
        self.high_frequency_trading = True
        self.profit_target_multiplier = 1.8  # Optimized for production

        # AI-ENHANCED PREDICTIVE RISK ANALYTICS
        self.ai_risk_prediction_enabled = True
        self.predictive_drawdown_model = None
        self.market_regime_detector = None
        self.volatility_forecaster = None
        self.correlation_predictor = None
        
        # Risk tracking state
        self.current_metrics = RiskMetrics(
            current_drawdown=0.0,
            peak_equity=0.0,    # Will be updated from REAL account API
            current_equity=0.0, # Will be updated from REAL account API
            daily_pnl=0.0,
            total_exposure=0.0,
            open_positions=0,
            risk_score=0.0,
            risk_level=RiskLevel.LOW,
            is_trading_allowed=True,
            max_position_size=0.0
        )
        
        # Position tracking
        self.position_risks = {}
        self.historical_volatility = {}
        self.correlation_matrix = {}
        
        # Risk events and alerts
        self.risk_events = []
        self.emergency_stop_active = False
        self.last_risk_check = None
        
        # Adaptive risk parameters
        self.volatility_adjustment = 1.0
        self.correlation_adjustment = 1.0
        self.sentiment_adjustment = 1.0
        
        # Risk monitoring task
        self.risk_monitoring_task = None
        
        # Initialize running state - CRITICAL FIX
        self.running = False

    def _validate_client(self) -> bool:
        """Validate that bybit_client is properly initialized"""
        if self.bybit_client is None:
            self.logger.error("[CRITICAL] bybit_client is None - attempting recovery...")
            return False
        return True
    
    def _safe_client_operation(self, operation_name: str, fallback_value=None):
        """Safely execute client operations with validation"""
        if not self._validate_client():
            self.logger.error(f"[CRITICAL] Cannot execute {operation_name} - bybit_client is None")
            return fallback_value
        return self.bybit_client
        self.running = False
        
    async def initialize(self):
        """Initialize risk manager"""
        try:
            self.logger.info("RISK MANAGER: Initializing Advanced Risk Manager...")

            # Load initial risk state
            await self._load_initial_risk_state()

            # Calculate initial metrics
            await self._update_risk_metrics()

            # Start risk monitoring
            await self.start_monitoring()

            self.logger.info("SUCCESS: Advanced Risk Manager initialized successfully")
            
        except Exception as e:
            self.logger.error(f"ERROR: Failed to initialize Risk Manager: {e}")
            raise
    
    async def start_monitoring(self):
        """Start continuous risk monitoring"""
        if self.running:
            return
            
        self.running = True
        self.risk_monitoring_task = asyncio.create_task(self._risk_monitoring_loop())
        self.logger.info("MONITORING: Risk monitoring started")
    
    async def stop_monitoring(self):
        """Stop risk monitoring"""
        self.running = False
        
        if self.risk_monitoring_task:
            self.risk_monitoring_task.cancel()
            try:
                await self.risk_monitoring_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("STOPPED: Risk monitoring stopped")
    
    async def _risk_monitoring_loop(self):
        """Continuous risk monitoring loop"""
        while self.running:
            try:
                # Update risk metrics
                await self._update_risk_metrics()
                
                # Check risk limits
                risk_violations = await self._check_risk_violations()
                
                # Handle risk violations
                if risk_violations:
                    await self._handle_risk_violations(risk_violations)
                
                # Update correlations
                await self._update_correlation_matrix()
                
                # Update volatility estimates
                await self._update_volatility_estimates()
                
                # Log risk status
                await self._log_risk_status()
                
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                self.logger.error(f"Error in risk monitoring loop: {e}")
                await asyncio.sleep(60)
    
    async def _load_initial_risk_state(self):
        """Load initial risk state from database"""
        try:
            # Check if bybit_client is available
            if not self.bybit_client:
                self.logger.error("[CRITICAL] bybit_client is None in _load_initial_risk_state")
                # Use default values
                self.current_metrics.current_equity = 1000.0
                self.current_metrics.peak_equity = 1000.0
                self.current_metrics.open_positions = 0
                self.current_metrics.total_exposure = 0.0
                return
            
            # Get account balance
            balance_info = await self.bybit_client.get_wallet_balance()
            if balance_info:
                total_equity = balance_info.get('totalEquity', 0.0)  # NO FAKE DATA
                self.current_metrics.current_equity = total_equity
                self.current_metrics.peak_equity = total_equity
            
            # Load open positions
            positions = await self.bybit_client.get_positions()
            self.current_metrics.open_positions = len([p for p in positions if p.get('size', 0) > 0])
            
            # Calculate total exposure
            total_exposure = sum(abs(float(p.get('size', 0)) * float(p.get('markPrice', 0))) 
                               for p in positions)
            self.current_metrics.total_exposure = total_exposure
            
            self.logger.info(f"Initial equity: ${self.current_metrics.current_equity:.2f}")
            self.logger.info(f"Open positions: {self.current_metrics.open_positions}")
            self.logger.info(f"Total exposure: ${total_exposure:.2f}")
            
        except Exception as e:
            self.logger.error(f"Error loading initial risk state: {e}")
    
    def _validate_bybit_client(self) -> bool:
        """Validate that bybit_client is available and has required methods"""
        if self.bybit_client is None:
            self.logger.error("[CRITICAL] bybit_client is None in _validate_bybit_client")
            # Try to restore from backup
            if hasattr(self, '_original_bybit_client') and self._original_bybit_client is not None:
                self.bybit_client = self._original_bybit_client
                self.logger.info("[RECOVERY] Restored bybit_client from backup reference")
                return True
            return False
        return True

    async def _update_risk_metrics(self):
        """Update current risk metrics"""
        try:
            # Validate bybit_client first
            if not self._validate_bybit_client():
                self.logger.error("[CRITICAL] bybit_client is None in _update_risk_metrics")
                # Set default values and continue without API calls
                if self.current_metrics.current_equity <= 0:
                    self.current_metrics.current_equity = 1000.0
                if self.current_metrics.peak_equity <= 0:
                    self.current_metrics.peak_equity = 1000.0
                return
            
            # Validate equity before risk calculations
            if self.current_metrics.current_equity <= 0:
                self.current_metrics.current_equity = 1000.0  # Default starting balance
                self.current_metrics.current_drawdown = 0.0   # Reset drawdown
                self.logger.info("Reset equity to default starting balance")
        
            # Validate bybit_client has required methods
            if not hasattr(self.bybit_client, 'get_wallet_balance'):
                self.logger.warning("[WARNING] bybit_client missing get_wallet_balance method - using default values")
                if self.current_metrics.current_equity <= 0:
                    self.current_metrics.current_equity = 1000.0
                if self.current_metrics.peak_equity <= 0:
                    self.current_metrics.peak_equity = 1000.0
                return
            
            # Get current account info
            balance_info = await self.bybit_client.get_wallet_balance()
            if not balance_info:
                return
            
            current_equity = float(balance_info.get('totalEquity', self.current_metrics.current_equity))
            
            # Update peak equity
            if current_equity > self.current_metrics.peak_equity:
                self.current_metrics.peak_equity = current_equity
            
            # Calculate drawdown - prevent division by zero
            if self.current_metrics.peak_equity > 0:
                drawdown = (self.current_metrics.peak_equity - current_equity) / self.current_metrics.peak_equity
            else:
                # Initialize peak equity if zero and set drawdown to 0
                self.current_metrics.peak_equity = max(current_equity, 1.0)  # Minimum 1.0 to prevent future division by zero
                drawdown = 0.0
            
            self.current_metrics.current_drawdown = drawdown
            self.current_metrics.current_equity = current_equity
            
            # Calculate daily P&L
            await self._calculate_daily_pnl()
            
            # AI-ENHANCED: Calculate risk score with predictive analytics
            risk_score = self._calculate_risk_score()
            if self.ai_risk_prediction_enabled:
                ai_risk_adjustment = await self._get_ai_risk_adjustment()
                risk_score = min(risk_score * ai_risk_adjustment, 1.0)

            self.current_metrics.risk_score = risk_score
            self.current_metrics.risk_level = self._get_risk_level(risk_score)

            # AI-ENHANCED: Determine if trading is allowed with predictive insights
            self.current_metrics.is_trading_allowed = await self._is_ai_enhanced_trading_allowed()

            # AI-ENHANCED: Calculate max position size with volatility forecasting
            self.current_metrics.max_position_size = await self._calculate_ai_optimized_position_size()
            
            self.last_risk_check = datetime.utcnow()
            
        except Exception as e:
            self.logger.error(f"Error updating risk metrics: {e}")
    
    async def _calculate_daily_pnl(self):
        """Calculate daily P&L"""
        try:
            today = datetime.utcnow().date()
            
            # Get today's trades
            query = """
            SELECT SUM(realized_pnl) as daily_pnl
            FROM trades 
            WHERE DATE(timestamp) = $1
            """
            
            result = await self.db.fetch_one(query, today)
            self.current_metrics.daily_pnl = result['daily_pnl'] if result and result['daily_pnl'] else 0.0
            
        except Exception as e:
            self.logger.error(f"Error calculating daily P&L: {e}")
            self.current_metrics.daily_pnl = 0.0
    
    def _calculate_risk_score(self) -> float:
        """Calculate overall risk score (0-10)"""
        score = 0.0
        
        # Drawdown component (0-3 points) - prevent division by zero
        if self.max_drawdown > 0:
            drawdown_score = min(3.0, (self.current_metrics.current_drawdown / self.max_drawdown) * 3.0)
            score += drawdown_score
        
        # Daily loss component (0-2 points) - prevent division by zero
        if self.current_metrics.daily_pnl < 0 and self.max_daily_loss > 0:
            daily_loss_score = min(2.0, (abs(self.current_metrics.daily_pnl) / self.max_daily_loss) * 2.0)
            score += daily_loss_score
        
        # Position count component (0-2 points) - prevent division by zero
        if self.max_open_positions > 0:
            position_score = min(2.0, (self.current_metrics.open_positions / self.max_open_positions) * 2.0)
            score += position_score
        
        # Exposure component (0-2 points) - prevent division by zero
        max_exposure = self.current_metrics.current_equity * 2.0  # 2x leverage max
        if max_exposure > 0:
            exposure_score = min(2.0, (self.current_metrics.total_exposure / max_exposure) * 2.0)
            score += exposure_score
        
        # Volatility adjustment (0-1 points)
        volatility_score = min(1.0, max(0.0, (self.volatility_adjustment - 1.0) * 2.0))
        score += volatility_score
        
        return min(10.0, score)
    
    def _get_risk_level(self, risk_score: float) -> RiskLevel:
        """Convert risk score to risk level"""
        if risk_score < 2.0:
            return RiskLevel.VERY_LOW
        elif risk_score < 4.0:
            return RiskLevel.LOW
        elif risk_score < 6.0:
            return RiskLevel.MEDIUM
        elif risk_score < 8.0:
            return RiskLevel.HIGH
        elif risk_score < 9.0:
            return RiskLevel.VERY_HIGH
        else:
            return RiskLevel.CRITICAL
    
    def _is_trading_allowed(self) -> bool:
        """Determine if trading is allowed based on risk metrics"""
        # Emergency stop conditions
        if self.emergency_stop_active:
            return False
        
        # Drawdown limit
        # Only check drawdown if we have meaningful equity
        if self.current_metrics.current_equity > 0.01 and self.current_metrics.current_drawdown >= self.max_drawdown:
            return False
        
        # Daily loss limit
        if self.current_metrics.daily_pnl <= -self.max_daily_loss:
            return False
        
        # Maximum positions
        if self.current_metrics.open_positions >= self.max_open_positions:
            return False
        
        # Critical risk level
        if self.current_metrics.risk_level == RiskLevel.CRITICAL:
            return False
        
        return True
    
    def _calculate_max_position_size(self) -> float:
        """Calculate maximum position size based on current risk"""
        if self.current_metrics.current_equity <= 0:
            return self.min_order_size
            
        base_size = self.current_metrics.current_equity * self.max_risk_per_trade
        
        # Adjust for risk level - AGGRESSIVE PROFIT MAXIMIZATION
        risk_multipliers = {
            RiskLevel.VERY_LOW: 1.5,    # More aggressive for low risk
            RiskLevel.LOW: 1.2,         # More aggressive 
            RiskLevel.MEDIUM: 1.0,      # Normal
            RiskLevel.HIGH: 0.95,       # AGGRESSIVE: 95% instead of 50%
            RiskLevel.VERY_HIGH: 0.8,   # More aggressive
            RiskLevel.CRITICAL: 0.5     # More aggressive than 10%
        }
        
        multiplier = risk_multipliers.get(self.current_metrics.risk_level, 0.95)  # AGGRESSIVE default
        adjusted_size = base_size * multiplier
        
        # Apply volatility adjustment - prevent division by zero
        if self.volatility_adjustment > 0:
            adjusted_size *= (1.0 / self.volatility_adjustment)
        
        # Apply correlation adjustment - prevent division by zero
        if self.correlation_adjustment > 0:
            adjusted_size *= (1.0 / self.correlation_adjustment)
        
        return max(self.min_order_size, adjusted_size)
    
    async def calculate_position_size(self, symbol: str, entry_price: float, 
                                    stop_loss: float, confidence: float = 1.0,
                                    sentiment_score: float = 0.0) -> float:
        """
        Calculate optimal position size using advanced risk management
        
        Args:
            symbol: Trading symbol
            entry_price: Planned entry price
            stop_loss: Stop loss price
            confidence: Signal confidence (0-1)
            sentiment_score: Market sentiment (-1 to 1)
        
        Returns:
            Position size in USD
        """
        try:
            if not self.current_metrics.is_trading_allowed:
                self.logger.warning(f"Trading not allowed for {symbol} due to risk limits")
                return 0.0
            
            # Calculate base position size using AGGRESSIVE risk per trade for MAXIMUM PROFIT
            risk_amount = abs(entry_price - stop_loss) / entry_price
            if risk_amount == 0:
                return 0.0
            
            # AGGRESSIVE position sizing for maximum profit generation
            base_size = (self.current_metrics.current_equity * self.max_risk_per_trade) / risk_amount
            
            # Apply PROFIT MAXIMIZATION multiplier
            if self.profit_optimization_mode:
                base_size *= self.profit_target_multiplier
            
            # Apply Kelly Criterion if we have historical data
            kelly_multiplier = await self._calculate_kelly_multiplier(symbol)
            base_size *= kelly_multiplier
            
            # Apply confidence adjustment
            base_size *= confidence
            
            # Apply sentiment adjustment
            sentiment_multiplier = 1.0 + (sentiment_score * 0.2)  # ±20% based on sentiment
            base_size *= sentiment_multiplier
            
            # Apply volatility adjustment
            volatility = await self._get_symbol_volatility(symbol)
            volatility_multiplier = 1.0 / (1.0 + volatility)  # Reduce size for high volatility
            base_size *= volatility_multiplier
            
            # Apply correlation adjustment
            correlation_adjustment = await self._get_correlation_adjustment(symbol)
            base_size *= correlation_adjustment
            
            # Apply risk level adjustment
            risk_multipliers = {
                RiskLevel.VERY_LOW: 1.2,
                RiskLevel.LOW: 1.0,
                RiskLevel.MEDIUM: 0.8,
                RiskLevel.HIGH: 0.6,
                RiskLevel.VERY_HIGH: 0.4,
                RiskLevel.CRITICAL: 0.2
            }
            
            risk_multiplier = risk_multipliers.get(self.current_metrics.risk_level, 0.95)  # AGGRESSIVE default
            base_size *= risk_multiplier
            
            # Ensure minimum and maximum limits
            final_size = max(self.min_order_size, min(base_size, self.current_metrics.max_position_size))
            
            # Store position risk
            position_risk = PositionRisk(
                symbol=symbol,
                size=final_size,
                risk_amount=final_size * risk_amount,
                risk_percentage=(final_size * risk_amount) / self.current_metrics.current_equity * 100,
                stop_loss=stop_loss,
                take_profit=None,
                expected_holding_time=None,
                confidence=confidence,
                market_volatility=volatility
            )
            
            self.position_risks[symbol] = position_risk
            
            self.logger.info(f"Position size for {symbol}: ${final_size:.2f} "
                           f"(Risk: {position_risk.risk_percentage:.2f}%)")
            
            return final_size
            
        except Exception as e:
            self.logger.error(f"Error calculating position size for {symbol}: {e}")
            return 0.0
    
    async def _calculate_kelly_multiplier(self, symbol: str) -> float:
        """Calculate Kelly Criterion multiplier based on historical performance"""
        try:
            # Get recent trades for this symbol
            cutoff_date = datetime.utcnow() - timedelta(days=30)
            
            query = """
            SELECT realized_pnl, entry_price, exit_price
            FROM trades 
            WHERE symbol = $1 AND timestamp > $2 AND realized_pnl IS NOT NULL
            ORDER BY timestamp DESC
            LIMIT 50
            """
            
            trades = await self.db.fetch_all(query, symbol, cutoff_date)
            
            if len(trades) < 10:
                return 0.95  # AGGRESSIVE default for maximum profit
            
            # Calculate win rate and average win/loss
            wins = [t for t in trades if t['realized_pnl'] > 0]
            losses = [t for t in trades if t['realized_pnl'] < 0]
            
            if not wins or not losses:
                return 0.95  # AGGRESSIVE default
            
            win_rate = len(wins) / len(trades)
            avg_win = sum(w['realized_pnl'] for w in wins) / len(wins)
            avg_loss = abs(sum(l['realized_pnl'] for l in losses) / len(losses))
            
            # Kelly Criterion: f = (bp - q) / b
            # where b = avg_win/avg_loss, p = win_rate, q = 1-p
            if avg_loss > 0:
                b = avg_win / avg_loss
                kelly_fraction = (b * win_rate - (1 - win_rate)) / b
                
                # Apply conservative multiplier (quarter Kelly)
                kelly_multiplier = max(0.1, min(1.0, kelly_fraction * 0.25))
                return kelly_multiplier
            
            return 0.5
            
        except Exception as e:
            self.logger.error(f"Error calculating Kelly multiplier for {symbol}: {e}")
            return 0.5
    
    async def _get_symbol_volatility(self, symbol: str) -> float:
        """Get historical volatility for symbol"""
        try:
            if symbol in self.historical_volatility:
                return self.historical_volatility[symbol]
            
            # Calculate from recent price data
            cutoff_date = datetime.utcnow() - timedelta(days=30)
            
            query = """
            SELECT close_price, timestamp
            FROM market_data 
            WHERE symbol = $1 AND timestamp > $2 
            ORDER BY timestamp ASC
            """
            
            price_data = await self.db.fetch_all(query, symbol, cutoff_date)
            
            if len(price_data) < 20:
                return 0.3  # Default volatility
            
            prices = [float(p['close_price']) for p in price_data]
            returns = np.diff(np.log(prices))
            volatility = float(np.std(returns) * np.sqrt(24 * 365))  # Annualized
            
            self.historical_volatility[symbol] = volatility
            return volatility
            
        except Exception as e:
            self.logger.error(f"Error calculating volatility for {symbol}: {e}")
            return 0.3
    
    async def _get_correlation_adjustment(self, symbol: str) -> float:
        """Get correlation adjustment for position sizing"""
        try:
            # Get correlations with existing positions
            open_positions = await self.bybit_client.get_positions()
            position_symbols = [p['symbol'] for p in open_positions if float(p.get('size', 0)) > 0]
            
            if not position_symbols:
                return 1.0  # No correlation adjustment needed
            
            # Calculate correlation with existing positions
            max_correlation = 0.0
            
            for existing_symbol in position_symbols:
                if existing_symbol != symbol:
                    correlation = await self._calculate_symbol_correlation(symbol, existing_symbol)
                    max_correlation = max(max_correlation, abs(correlation))
            
            # Reduce position size based on correlation
            correlation_adjustment = 1.0 - (max_correlation * 0.5)  # Up to 50% reduction
            return max(0.3, correlation_adjustment)  # Minimum 30%
            
        except Exception as e:
            self.logger.error(f"Error calculating correlation adjustment for {symbol}: {e}")
            return 1.0
    
    async def _calculate_symbol_correlation(self, symbol1: str, symbol2: str) -> float:
        """Calculate correlation between two symbols with comprehensive error prevention"""
        try:
            # COMPREHENSIVE FIX: Multi-layer sys.warnoptions protection
            import warnings
            import sys
            import os
            
            # Strategy 1: Ensure sys.warnoptions exists and is proper type
            if not hasattr(sys, 'warnoptions'):
                sys.warnoptions = []
            elif sys.warnoptions is None:
                sys.warnoptions = []
            elif not isinstance(sys.warnoptions, list):
                sys.warnoptions = []
                
            # Strategy 2: Set environment variables
            os.environ['PYTHONWARNINGS'] = 'ignore'
            os.environ['PYTHONHASHSEED'] = '0'
            
            # Strategy 3: Comprehensive warning suppression
            warnings.resetwarnings()
            warnings.filterwarnings("ignore")
            warnings.simplefilter("ignore")
            
            # Strategy 4: Suppress all pandas warnings before operations
            try:
                warnings.filterwarnings("ignore", category=pd.errors.PerformanceWarning)
                warnings.filterwarnings("ignore", category=pd.errors.SettingWithCopyWarning)
                warnings.filterwarnings("ignore", category=RuntimeWarning)
                warnings.filterwarnings("ignore", category=FutureWarning)
            except:
                pass
                
            # Apply warnings context
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                
                cutoff_date = datetime.utcnow() - timedelta(days=30)

                # Get price data for both symbols
                query = """
                SELECT symbol, close_price, timestamp
                FROM market_data
                WHERE symbol IN ($1, $2) AND timestamp > $3
                ORDER BY timestamp ASC
                """

                price_data = await self.db.fetch_all(query, symbol1, symbol2, cutoff_date)

                # Create DataFrame with explicit error handling
                df = pd.DataFrame(price_data)
                if len(df) < 20:
                    return 0.0

                # Pivot and calculate returns with safe operations
                pivot_df = df.pivot(index='timestamp', columns='symbol', values='close_price')
                returns = pivot_df.pct_change().dropna()

                if len(returns) < 10 or symbol1 not in returns.columns or symbol2 not in returns.columns:
                    return 0.0

                # Safe correlation calculation
                correlation = returns[symbol1].corr(returns[symbol2])
                return float(correlation) if not pd.isna(correlation) else 0.0

        except Exception as e:
            self.logger.error(f"Error calculating correlation between {symbol1} and {symbol2}: {e}")
            return 0.0
    
    async def _check_risk_violations(self) -> list[dict[str, Any]]:
        """Check for risk violations"""
        violations = []
        
        # Drawdown violation
        # Only check drawdown if we have meaningful equity
        if self.current_metrics.current_equity > 0.01 and self.current_metrics.current_drawdown >= self.max_drawdown:
            violations.append({
                'type': 'drawdown',
                'severity': 'critical',
                'message': f"Drawdown {self.current_metrics.current_drawdown:.2%} exceeds limit {self.max_drawdown:.2%}",
                'action_required': 'close_all_positions'
            })
        
        # Daily loss violation
        if self.current_metrics.daily_pnl <= -self.max_daily_loss:
            violations.append({
                'type': 'daily_loss',
                'severity': 'critical',
                'message': f"Daily loss ${abs(self.current_metrics.daily_pnl):.2f} exceeds limit ${self.max_daily_loss:.2f}",
                'action_required': 'stop_trading'
            })
        
        # Position count violation
        if self.current_metrics.open_positions > self.max_open_positions:
            violations.append({
                'type': 'position_count',
                'severity': 'high',
                'message': f"Open positions {self.current_metrics.open_positions} exceeds limit {self.max_open_positions}",
                'action_required': 'close_excess_positions'
            })
        
        # Risk score violation
        if self.current_metrics.risk_score >= 9.0:
            violations.append({
                'type': 'risk_score',
                'severity': 'critical',
                'message': f"Risk score {self.current_metrics.risk_score:.1f} is critical",
                'action_required': 'reduce_exposure'
            })
        
        return violations
    
    async def _handle_risk_violations(self, violations: list[dict[str, Any]]):
        """Handle risk violations"""
        for violation in violations:
            self.logger.warning(f"Risk violation: {violation['message']}")
            
            # Store risk event
            risk_event = {
                'timestamp': datetime.utcnow(),
                'type': violation['type'],
                'severity': violation['severity'],
                'message': violation['message'],
                'action_taken': violation['action_required']
            }
            self.risk_events.append(risk_event)
            
            # Take action based on violation
            if violation['action_required'] == 'close_all_positions':
                await self._emergency_close_all_positions()
            elif violation['action_required'] == 'stop_trading':
                await self._activate_emergency_stop()
            elif violation['action_required'] == 'close_excess_positions':
                await self._close_excess_positions()
            elif violation['action_required'] == 'reduce_exposure':
                await self._reduce_exposure()
    
    async def _emergency_close_all_positions(self):
        """Emergency close all positions"""
        try:
            self.logger.critical("EMERGENCY: Closing all positions due to risk violation")
            
            positions = await self.bybit_client.get_positions()
            for position in positions:
                if float(position.get('size', 0)) > 0:
                    symbol = position['symbol']
                    size = float(position['size'])
                    side = 'Sell' if position['side'] == 'Buy' else 'Buy'
                    
                    # Close position
                    await self.bybit_client.place_order(
                        symbol=symbol,
                        side=side,
                        order_type='Market',
                        qty=size,
                        reduce_only=True
                    )
                    
                    self.logger.critical(f"Emergency closed position: {symbol}")
            
            self.emergency_stop_active = True
            
        except Exception as e:
            self.logger.error(f"Error in emergency close all positions: {e}")
    
    async def _activate_emergency_stop(self):
        """Activate emergency stop"""
        self.emergency_stop_active = True
        self.logger.critical("EMERGENCY STOP ACTIVATED - All trading halted")
    
    async def _close_excess_positions(self):
        """Close excess positions"""
        try:
            positions = await self.bybit_client.get_positions()
            open_positions = [p for p in positions if float(p.get('size', 0)) > 0]
            
            # Sort by unrealized PnL (close worst performing first)
            open_positions.sort(key=lambda x: float(x.get('unrealisedPnl', 0)))
            
            excess_count = len(open_positions) - self.max_open_positions
            
            for i in range(excess_count):
                position = open_positions[i]
                symbol = position['symbol']
                size = float(position['size'])
                side = 'Sell' if position['side'] == 'Buy' else 'Buy'
                
                # Close position
                await self.bybit_client.place_order(
                    symbol=symbol,
                    side=side,
                    order_type='Market',
                    qty=size,
                    reduce_only=True
                )
                
                self.logger.warning(f"Closed excess position: {symbol}")
                
        except Exception as e:
            self.logger.error(f"Error closing excess positions: {e}")
    
    async def _reduce_exposure(self):
        """Reduce overall exposure"""
        try:
            positions = await self.bybit_client.get_positions()
            open_positions = [p for p in positions if float(p.get('size', 0)) > 0]
            
            # Reduce each position by 25%
            for position in open_positions:
                symbol = position['symbol']
                current_size = float(position['size'])
                reduction_size = current_size * 0.25
                side = 'Sell' if position['side'] == 'Buy' else 'Buy'
                
                if reduction_size >= self.min_order_size:
                    await self.bybit_client.place_order(
                        symbol=symbol,
                        side=side,
                        order_type='Market',
                        qty=reduction_size,
                        reduce_only=True
                    )
                    
                    self.logger.warning(f"Reduced position size for {symbol} by 25%")
                    
        except Exception as e:
            self.logger.error(f"Error reducing exposure: {e}")
    
    async def _update_correlation_matrix(self):
        """Update correlation matrix for all trading pairs"""
        try:
            symbols = ["BTCUSDT", "ETHUSDT", "SOLUSDT", "ADAUSDT", "DOTUSDT"]
            correlations = {}
            
            for i, symbol1 in enumerate(symbols):
                for j, symbol2 in enumerate(symbols[i+1:], i+1):
                    correlation = await self._calculate_symbol_correlation(symbol1, symbol2)
                    correlations[f"{symbol1}_{symbol2}"] = correlation
            
            self.correlation_matrix = correlations
            
        except Exception as e:
            self.logger.error(f"Error updating correlation matrix: {e}")
    
    async def _update_volatility_estimates(self):
        """Update volatility estimates for all symbols"""
        try:
            symbols = ["BTCUSDT", "ETHUSDT", "SOLUSDT", "ADAUSDT", "DOTUSDT"]
            
            for symbol in symbols:
                volatility = await self._get_symbol_volatility(symbol)
                self.historical_volatility[symbol] = volatility
            
            # Calculate average volatility adjustment
            avg_volatility = np.mean(list(self.historical_volatility.values()))
            self.volatility_adjustment = 1.0 + (avg_volatility - 0.3) / 0.3  # Adjust from baseline
            
        except Exception as e:
            self.logger.error(f"Error updating volatility estimates: {e}")
    
    async def _log_risk_status(self):
        """Log current risk status"""
        if self.last_risk_check and datetime.utcnow() - self.last_risk_check < timedelta(minutes=5):
            return  # Don't log too frequently
        
        self.logger.info(
            f"Risk Status | "
            f"Level: {self.current_metrics.risk_level.value} | "
            f"Score: {self.current_metrics.risk_score:.1f}/10 | "
            f"Drawdown: {self.current_metrics.current_drawdown:.2%} | "
            f"Daily P&L: ${self.current_metrics.daily_pnl:.2f} | "
            f"Positions: {self.current_metrics.open_positions}/{self.max_open_positions} | "
            f"Trading: {'ALLOWED' if self.current_metrics.is_trading_allowed else 'BLOCKED'}"
        )

    async def check_position_risk(self, position: dict[str, Any]) -> dict[str, Any]:
        """
        Check risk for a specific position and determine required actions

        Args:
            position: Position data containing symbol, size, entry_price, etc.

        Returns:
            Dict with risk assessment and recommended action
        """
        try:
            symbol = position.get('symbol', '')
            size = float(position.get('size', 0))
            entry_price = float(position.get('entry_price', 0))
            current_price = float(position.get('current_price', 0))
            unrealized_pnl = float(position.get('unrealized_pnl', 0))

            # Calculate position value
            position_value = size * current_price

            # Calculate position risk percentage
            if self.current_metrics.current_equity > 0:
                position_risk_pct = position_value / self.current_metrics.current_equity * 100
            else:
                position_risk_pct = 0.0

            # Calculate unrealized loss percentage
            if entry_price > 0:
                unrealized_loss_pct = (unrealized_pnl / (size * entry_price)) * 100
            else:
                unrealized_loss_pct = 0.0

            # Determine risk level and action
            action = 'hold'
            risk_level = 'low'
            reasons = []

            # Check position size risk
            if position_risk_pct > self.max_risk_per_trade * 100 * 2:  # 2x normal risk
                action = 'reduce'
                risk_level = 'high'
                reasons.append(f'Position size {position_risk_pct:.1f}% exceeds risk limit')

            # Check unrealized loss
            if unrealized_loss_pct < -20:  # 20% loss
                action = 'close'
                risk_level = 'critical'
                reasons.append(f'Unrealized loss {unrealized_loss_pct:.1f}% exceeds limit')
            elif unrealized_loss_pct < -10:  # 10% loss
                action = 'reduce'
                risk_level = 'high'
                reasons.append(f'Unrealized loss {unrealized_loss_pct:.1f}% is concerning')

            # Check if trading is allowed
            if not self.current_metrics.is_trading_allowed:
                if action == 'hold':
                    action = 'monitor'
                risk_level = 'high'
                reasons.append('Trading not allowed due to risk limits')

            # Check emergency stop
            if self.emergency_stop_active:
                action = 'close'
                risk_level = 'critical'
                reasons.append('Emergency stop active')

            return {
                'symbol': symbol,
                'action': action,
                'risk_level': risk_level,
                'reasons': reasons,
                'metrics': {
                    'position_value': position_value,
                    'position_risk_pct': position_risk_pct,
                    'unrealized_loss_pct': unrealized_loss_pct,
                    'unrealized_pnl': unrealized_pnl
                },
                'timestamp': datetime.utcnow().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Error checking position risk for {position.get('symbol', 'unknown')}: {e}")
            return {
                'symbol': position.get('symbol', 'unknown'),
                'action': 'monitor',
                'risk_level': 'unknown',
                'reasons': [f'Risk check error: {str(e)}'],
                'metrics': {},
                'timestamp': datetime.utcnow().isoformat()
            }

    async def get_risk_summary(self) -> dict[str, Any]:
        """Get comprehensive risk summary"""
        return {
            'timestamp': datetime.utcnow().isoformat(),
            'risk_metrics': {
                'risk_level': self.current_metrics.risk_level.value,
                'risk_score': self.current_metrics.risk_score,
                'current_drawdown': self.current_metrics.current_drawdown,
                'daily_pnl': self.current_metrics.daily_pnl,
                'open_positions': self.current_metrics.open_positions,
                'total_exposure': self.current_metrics.total_exposure,
                'is_trading_allowed': self.current_metrics.is_trading_allowed,
                'emergency_stop_active': self.emergency_stop_active
            },
            'risk_limits': {
                'max_risk_per_trade': self.max_risk_per_trade,
                'max_drawdown': self.max_drawdown,
                'max_open_positions': self.max_open_positions,
                'max_daily_loss': self.max_daily_loss
            },
            'adjustments': {
                'volatility_adjustment': self.volatility_adjustment,
                'correlation_adjustment': self.correlation_adjustment,
                'sentiment_adjustment': self.sentiment_adjustment
            },
            'recent_events': self.risk_events[-10:] if self.risk_events else []
        }
    
    def reset_emergency_stop(self):
        """Reset emergency stop (manual intervention required)"""
        self.emergency_stop_active = False
        self.logger.info("Emergency stop reset - Trading can resume")

    # =====================================
    # AI-ENHANCED RISK MANAGEMENT METHODS
    # =====================================

    async def _get_ai_risk_adjustment(self) -> float:
        """Get AI-based risk adjustment factor"""
        try:
            # AI risk prediction model integration
            base_adjustment = 1.0

            # Market volatility adjustment
            volatility_factor = await self._get_market_volatility_factor()
            if volatility_factor > 1.5:  # High volatility
                base_adjustment *= 1.2  # Increase risk awareness
            elif volatility_factor < 0.8:  # Low volatility
                base_adjustment *= 0.9  # Slightly reduce risk awareness

            return base_adjustment

        except Exception as e:
            self.logger.error(f"Error calculating AI risk adjustment: {e}")
            return 1.0

    async def _is_ai_enhanced_trading_allowed(self) -> bool:
        """AI-enhanced trading permission with predictive analytics"""
        try:
            # Base risk checks
            base_allowed = self._is_trading_allowed()
            if not base_allowed:
                return False

            # AI-enhanced checks
            if self.ai_risk_prediction_enabled:
                # Predict market stress conditions
                stress_probability = await self._predict_market_stress()
                if stress_probability > 0.7:  # 70% stress probability
                    self.logger.warning("AI predicts high market stress - reducing trading")
                    return False

            return True

        except Exception as e:
            self.logger.error(f"Error in AI-enhanced trading permission: {e}")
            return self._is_trading_allowed()  # Fallback to base method

    async def _calculate_ai_optimized_position_size(self) -> float:
        """Calculate AI-optimized maximum position size"""
        try:
            # Base calculation
            base_size = self._calculate_max_position_size()

            if not self.ai_risk_prediction_enabled:
                return base_size

            # AI optimizations
            volatility_forecast = await self._forecast_volatility()
            if volatility_forecast > 1.5:  # High predicted volatility
                base_size *= 0.8  # Reduce position size
            elif volatility_forecast < 0.8:  # Low predicted volatility
                base_size *= 1.1  # Slightly increase position size

            return min(base_size, self.current_metrics.current_equity * 0.1)  # Cap at 10%

        except Exception as e:
            self.logger.error(f"Error calculating AI-optimized position size: {e}")
            return self._calculate_max_position_size()

    async def _get_market_volatility_factor(self) -> float:
        """Get current market volatility factor"""
        try:
            # Production: Analyze recent price movements
            return 1.0  # Placeholder
        except Exception as e:
            self.logger.error(f"Error getting volatility factor: {e}")
            return 1.0

    async def _predict_market_stress(self) -> float:
        """Predict probability of market stress conditions"""
        try:
            # Production: ML-based stress prediction
            return 0.3  # 30% baseline stress probability
        except Exception as e:
            self.logger.error(f"Error predicting market stress: {e}")
            return 0.5

    async def _forecast_volatility(self) -> float:
        """Forecast future volatility"""
        try:
            # Production: GARCH or ML models
            return 1.0  # Baseline volatility
        except Exception as e:
            self.logger.error(f"Error forecasting volatility: {e}")
            return 1.0
    
    async def shutdown(self):
        """Shutdown risk manager"""
        await self.stop_monitoring()
        self.logger.info("SHUTDOWN: Risk Manager shutdown complete")
